{% extends 'base.html' %}

{% block title %}Prediction History{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <h2 class="card-title mb-0">
                    <i class="fas fa-history"></i> Prediction History
                </h2>
            </div>
            <div class="card-body">
                {% if predictions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Prediction ID</th>
                                    <th>Property Details</th>
                                    <th>Predicted Price</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for prediction in predictions %}
                                <tr>
                                    <td>
                                        <code class="small">{{ prediction.prediction_id|truncatechars:13 }}...</code>
                                    </td>
                                    <td>
                                        <div class="small">
                                            <strong>{{ prediction.bedrooms }}BR/{{ prediction.bathrooms }}BA</strong><br>
                                            {{ prediction.sqft_living|floatformat:0 }} sq ft<br>
                                            Built: {{ prediction.yr_built }}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="h5 text-success">
                                            ${{ prediction.predicted_price|floatformat:0|add:"0"|stringformat:"s"|slice:"::-3"|join:"," }}{{ prediction.predicted_price|floatformat:0|add:"0"|stringformat:"s"|slice:"-3:" }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="small">
                                            {{ prediction.created_at|date:"M d, Y" }}<br>
                                            {{ prediction.created_at|time:"H:i" }}
                                        </div>
                                    </td>
                                    <td>
                                        <a href="{% url 'prediction_result' prediction.prediction_id %}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Summary Statistics -->
                    <div class="row mt-4">
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Total Predictions</h5>
                                    <h3 class="text-primary">{{ predictions|length }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Avg Price</h5>
                                    <h3 class="text-success">
                                        {% with avg_price=predictions|first.predicted_price %}
                                            ${{ avg_price|floatformat:0 }}K
                                        {% endwith %}
                                    </h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Latest</h5>
                                    <h3 class="text-info">{{ predictions.first.created_at|timesince }} ago</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <a href="{% url 'home' %}" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> New Prediction
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No Predictions Yet</h4>
                        <p class="text-muted">Start by making your first house price prediction!</p>
                        <a href="{% url 'home' %}" class="btn btn-primary btn-lg">
                            <i class="fas fa-plus"></i> Make First Prediction
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
