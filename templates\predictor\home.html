{% extends 'base.html' %}

{% block title %}US House Price Prediction - Home{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h2 class="card-title mb-0">
                    <i class="fas fa-calculator"></i> US House Price Prediction
                </h2>
            </div>
            <div class="card-body">
                <p class="lead">Enter the details of your property to get an estimated market price using our advanced machine learning model.</p>
                
                <form method="post" action="{% url 'predict_price' %}" id="predictionForm">
                    {% csrf_token %}
                    
                    <!-- Basic Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">Basic Information</h5>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.year.id_for_label }}" class="form-label">{{ form.year.label }}</label>
                            {{ form.year }}
                            {% if form.year.errors %}
                                <div class="text-danger small">{{ form.year.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.bedrooms.id_for_label }}" class="form-label">{{ form.bedrooms.label }}</label>
                            {{ form.bedrooms }}
                            {% if form.bedrooms.errors %}
                                <div class="text-danger small">{{ form.bedrooms.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.bathrooms.id_for_label }}" class="form-label">{{ form.bathrooms.label }}</label>
                            {{ form.bathrooms }}
                            {% if form.bathrooms.errors %}
                                <div class="text-danger small">{{ form.bathrooms.errors }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Property Size -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">Property Size</h5>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.sqft_living.id_for_label }}" class="form-label">{{ form.sqft_living.label }}</label>
                            {{ form.sqft_living }}
                            {% if form.sqft_living.errors %}
                                <div class="text-danger small">{{ form.sqft_living.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.sqft_lot.id_for_label }}" class="form-label">{{ form.sqft_lot.label }}</label>
                            {{ form.sqft_lot }}
                            {% if form.sqft_lot.errors %}
                                <div class="text-danger small">{{ form.sqft_lot.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.sqft_above.id_for_label }}" class="form-label">{{ form.sqft_above.label }}</label>
                            {{ form.sqft_above }}
                            {% if form.sqft_above.errors %}
                                <div class="text-danger small">{{ form.sqft_above.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.sqft_basement.id_for_label }}" class="form-label">{{ form.sqft_basement.label }}</label>
                            {{ form.sqft_basement }}
                            {% if form.sqft_basement.errors %}
                                <div class="text-danger small">{{ form.sqft_basement.errors }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Property Features -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">Property Features</h5>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.floors.id_for_label }}" class="form-label">{{ form.floors.label }}</label>
                            {{ form.floors }}
                            {% if form.floors.errors %}
                                <div class="text-danger small">{{ form.floors.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.view.id_for_label }}" class="form-label">{{ form.view.label }}</label>
                            {{ form.view }}
                            {% if form.view.errors %}
                                <div class="text-danger small">{{ form.view.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.condition.id_for_label }}" class="form-label">{{ form.condition.label }}</label>
                            {{ form.condition }}
                            {% if form.condition.errors %}
                                <div class="text-danger small">{{ form.condition.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.grade.id_for_label }}" class="form-label">{{ form.grade.label }}</label>
                            {{ form.grade }}
                            {% if form.grade.errors %}
                                <div class="text-danger small">{{ form.grade.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="form-check mt-4">
                                {{ form.waterfront }}
                                <label class="form-check-label" for="{{ form.waterfront.id_for_label }}">
                                    {{ form.waterfront.label }}
                                </label>
                            </div>
                            {% if form.waterfront.errors %}
                                <div class="text-danger small">{{ form.waterfront.errors }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Property History -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">Property History</h5>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.yr_built.id_for_label }}" class="form-label">{{ form.yr_built.label }}</label>
                            {{ form.yr_built }}
                            {% if form.yr_built.errors %}
                                <div class="text-danger small">{{ form.yr_built.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.yr_renovated.id_for_label }}" class="form-label">{{ form.yr_renovated.label }}</label>
                            {{ form.yr_renovated }}
                            {% if form.yr_renovated.errors %}
                                <div class="text-danger small">{{ form.yr_renovated.errors }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Location & Neighborhood -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">Location & Neighborhood</h5>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.lat.id_for_label }}" class="form-label">{{ form.lat.label }}</label>
                            {{ form.lat }}
                            {% if form.lat.errors %}
                                <div class="text-danger small">{{ form.lat.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.sqft_living15.id_for_label }}" class="form-label">{{ form.sqft_living15.label }}</label>
                            {{ form.sqft_living15 }}
                            {% if form.sqft_living15.errors %}
                                <div class="text-danger small">{{ form.sqft_living15.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.sqft_lot15.id_for_label }}" class="form-label">{{ form.sqft_lot15.label }}</label>
                            {{ form.sqft_lot15 }}
                            {% if form.sqft_lot15.errors %}
                                <div class="text-danger small">{{ form.sqft_lot15.errors }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Form Errors -->
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-magic"></i> Predict House Price
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Quick Tips -->
<div class="row mt-5">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-lightbulb"></i> Tips for Accurate Predictions</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> Ensure living space equals above ground + basement space</li>
                    <li><i class="fas fa-check text-success"></i> Renovation year should be after the year built</li>
                    <li><i class="fas fa-check text-success"></i> Use accurate square footage measurements</li>
                    <li><i class="fas fa-check text-success"></i> Consider neighborhood characteristics for better accuracy</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-calculate total living space
document.addEventListener('DOMContentLoaded', function() {
    const sqftAbove = document.getElementById('id_sqft_above');
    const sqftBasement = document.getElementById('id_sqft_basement');
    const sqftLiving = document.getElementById('id_sqft_living');
    
    function updateLivingSpace() {
        const above = parseInt(sqftAbove.value) || 0;
        const basement = parseInt(sqftBasement.value) || 0;
        sqftLiving.value = above + basement;
    }
    
    sqftAbove.addEventListener('input', updateLivingSpace);
    sqftBasement.addEventListener('input', updateLivingSpace);
});
</script>
{% endblock %}
