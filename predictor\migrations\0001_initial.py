# Generated by Django 5.2 on 2025-07-30 10:40

import django.utils.timezone
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='PredictionRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('prediction_id', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('year', models.IntegerField(help_text='Year of sale')),
                ('bedrooms', models.IntegerField(help_text='Number of bedrooms')),
                ('bathrooms', models.FloatField(help_text='Number of bathrooms')),
                ('sqft_living', models.IntegerField(help_text='Square footage of living space')),
                ('sqft_lot', models.IntegerField(help_text='Square footage of lot')),
                ('floors', models.FloatField(help_text='Number of floors')),
                ('waterfront', models.BooleanField(default=False, help_text='Waterfront property')),
                ('view', models.IntegerField(default=0, help_text='View rating (0-4)')),
                ('condition', models.IntegerField(default=3, help_text='Condition rating (1-5)')),
                ('grade', models.IntegerField(default=7, help_text='Grade rating (1-13)')),
                ('sqft_above', models.IntegerField(help_text='Square footage above ground')),
                ('sqft_basement', models.IntegerField(default=0, help_text='Square footage of basement')),
                ('yr_built', models.IntegerField(help_text='Year built')),
                ('yr_renovated', models.IntegerField(default=0, help_text='Year renovated (0 if never)')),
                ('lat', models.FloatField(help_text='Latitude')),
                ('sqft_living15', models.IntegerField(help_text='Living space of nearest 15 neighbors')),
                ('sqft_lot15', models.IntegerField(help_text='Lot size of nearest 15 neighbors')),
                ('is_renovated', models.BooleanField(default=False, help_text='Has been renovated')),
                ('total_room', models.IntegerField(help_text='Total rooms (bedrooms + bathrooms)')),
                ('predicted_price', models.FloatField(blank=True, help_text='Predicted price', null=True)),
                ('log_predicted_price', models.FloatField(blank=True, help_text='Log predicted price', null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PriceTrend',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField()),
                ('month', models.IntegerField()),
                ('average_price', models.FloatField()),
                ('median_price', models.FloatField()),
                ('count', models.IntegerField()),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'ordering': ['year', 'month'],
                'unique_together': {('year', 'month')},
            },
        ),
    ]
