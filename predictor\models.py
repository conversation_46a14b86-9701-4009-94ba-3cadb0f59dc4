from django.db import models
import uuid
from django.utils import timezone

class PredictionRequest(models.Model):
    """Model to store house price prediction requests and results"""

    # Unique identifier for tracking predictions
    prediction_id = models.UUIDField(default=uuid.uuid4, unique=True, editable=False)

    # Input features from the original model
    year = models.IntegerField(help_text="Year of sale")
    bedrooms = models.IntegerField(help_text="Number of bedrooms")
    bathrooms = models.FloatField(help_text="Number of bathrooms")
    sqft_living = models.IntegerField(help_text="Square footage of living space")
    sqft_lot = models.IntegerField(help_text="Square footage of lot")
    floors = models.FloatField(help_text="Number of floors")
    waterfront = models.BooleanField(default=False, help_text="Waterfront property")
    view = models.IntegerField(default=0, help_text="View rating (0-4)")
    condition = models.IntegerField(default=3, help_text="Condition rating (1-5)")
    grade = models.IntegerField(default=7, help_text="Grade rating (1-13)")
    sqft_above = models.IntegerField(help_text="Square footage above ground")
    sqft_basement = models.IntegerField(default=0, help_text="Square footage of basement")
    yr_built = models.IntegerField(help_text="Year built")
    yr_renovated = models.IntegerField(default=0, help_text="Year renovated (0 if never)")
    lat = models.FloatField(help_text="Latitude")
    sqft_living15 = models.IntegerField(help_text="Living space of nearest 15 neighbors")
    sqft_lot15 = models.IntegerField(help_text="Lot size of nearest 15 neighbors")

    # Calculated fields
    is_renovated = models.BooleanField(default=False, help_text="Has been renovated")
    total_room = models.IntegerField(help_text="Total rooms (bedrooms + bathrooms)")

    # Prediction results
    predicted_price = models.FloatField(null=True, blank=True, help_text="Predicted price")
    log_predicted_price = models.FloatField(null=True, blank=True, help_text="Log predicted price")

    # Metadata
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Prediction {self.prediction_id} - ${self.predicted_price:,.2f}" if self.predicted_price else f"Prediction {self.prediction_id}"

    def save(self, *args, **kwargs):
        # Calculate derived fields
        self.is_renovated = self.yr_renovated > 0
        self.total_room = self.bedrooms + int(self.bathrooms)
        super().save(*args, **kwargs)


class PriceTrend(models.Model):
    """Model to store historical price trends for visualization"""

    year = models.IntegerField()
    month = models.IntegerField()
    average_price = models.FloatField()
    median_price = models.FloatField()
    count = models.IntegerField()
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        unique_together = ['year', 'month']
        ordering = ['year', 'month']

    def __str__(self):
        return f"{self.year}-{self.month:02d}: ${self.average_price:,.2f}"
