"""
Django forms for house price prediction input
"""

from django import forms
from .models import PredictionRequest
from datetime import datetime

class HousePredictionForm(forms.ModelForm):
    """Form for collecting house features for price prediction"""
    
    class Meta:
        model = PredictionRequest
        fields = [
            'year', 'bedrooms', 'bathrooms', 'sqft_living', 'sqft_lot',
            'floors', 'waterfront', 'view', 'condition', 'grade',
            'sqft_above', 'sqft_basement', 'yr_built', 'yr_renovated', 'lat',
            'sqft_living15', 'sqft_lot15'
        ]
        
        widgets = {
            'year': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 2000,
                'max': datetime.now().year,
                'value': datetime.now().year
            }),
            'bedrooms': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 1,
                'max': 10,
                'value': 3
            }),
            'bathrooms': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 1,
                'max': 8,
                'step': 0.5,
                'value': 2
            }),
            'sqft_living': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 500,
                'max': 10000,
                'value': 2000
            }),
            'sqft_lot': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 1000,
                'max': 100000,
                'value': 7500
            }),
            'floors': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 1,
                'max': 3.5,
                'step': 0.5,
                'value': 1
            }),
            'waterfront': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'view': forms.Select(choices=[
                (0, 'No View'),
                (1, 'Fair'),
                (2, 'Average'),
                (3, 'Good'),
                (4, 'Excellent')
            ], attrs={'class': 'form-select'}),
            'condition': forms.Select(choices=[
                (1, 'Poor'),
                (2, 'Fair'),
                (3, 'Average'),
                (4, 'Good'),
                (5, 'Very Good')
            ], attrs={'class': 'form-select'}),
            'grade': forms.Select(choices=[
                (3, 'Poor'),
                (4, 'Low'),
                (5, 'Fair'),
                (6, 'Low Average'),
                (7, 'Average'),
                (8, 'Good'),
                (9, 'Better'),
                (10, 'Very Good'),
                (11, 'Excellent'),
                (12, 'Luxury'),
                (13, 'Mansion')
            ], attrs={'class': 'form-select'}),
            'sqft_above': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 500,
                'max': 8000,
                'value': 1500
            }),
            'sqft_basement': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 0,
                'max': 3000,
                'value': 0
            }),
            'yr_built': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 1900,
                'max': datetime.now().year,
                'value': 1990
            }),
            'yr_renovated': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 0,
                'max': datetime.now().year,
                'value': 0
            }),
            'lat': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 47.0,
                'max': 48.0,
                'step': 0.0001,
                'value': 47.5
            }),
            'sqft_living15': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 500,
                'max': 6000,
                'value': 2000
            }),
            'sqft_lot15': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 1000,
                'max': 50000,
                'value': 7500
            }),
        }
        
        labels = {
            'year': 'Sale Year',
            'bedrooms': 'Number of Bedrooms',
            'bathrooms': 'Number of Bathrooms',
            'sqft_living': 'Living Space (sq ft)',
            'sqft_lot': 'Lot Size (sq ft)',
            'floors': 'Number of Floors',
            'waterfront': 'Waterfront Property',
            'view': 'View Quality',
            'condition': 'Property Condition',
            'grade': 'Construction Grade',
            'sqft_above': 'Above Ground Space (sq ft)',
            'sqft_basement': 'Basement Space (sq ft)',
            'yr_built': 'Year Built',
            'yr_renovated': 'Year Renovated (0 if never)',
            'lat': 'Latitude',
            'sqft_living15': 'Avg Living Space of 15 Nearest Neighbors (sq ft)',
            'sqft_lot15': 'Avg Lot Size of 15 Nearest Neighbors (sq ft)',
        }
    
    def clean(self):
        cleaned_data = super().clean()
        
        # Validation: sqft_above + sqft_basement should roughly equal sqft_living
        sqft_living = cleaned_data.get('sqft_living')
        sqft_above = cleaned_data.get('sqft_above')
        sqft_basement = cleaned_data.get('sqft_basement')
        
        if sqft_living and sqft_above and sqft_basement:
            total_sqft = sqft_above + sqft_basement
            if abs(total_sqft - sqft_living) > 200:  # Allow some tolerance
                raise forms.ValidationError(
                    "Living space should roughly equal above ground + basement space"
                )
        
        # Validation: yr_renovated should be >= yr_built
        yr_built = cleaned_data.get('yr_built')
        yr_renovated = cleaned_data.get('yr_renovated')
        
        if yr_built and yr_renovated and yr_renovated > 0:
            if yr_renovated < yr_built:
                raise forms.ValidationError(
                    "Renovation year cannot be before the year built"
                )
        
        return cleaned_data


class PredictionSearchForm(forms.Form):
    """Form for searching predictions by ID"""
    
    prediction_id = forms.UUIDField(
        label='Prediction ID',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter prediction ID (e.g., 123e4567-e89b-12d3-a456-************)'
        })
    )
