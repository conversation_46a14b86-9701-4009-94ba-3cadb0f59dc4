{% extends 'base.html' %}

{% block title %}Prediction Result - {{ prediction.prediction_id }}{% endblock %}

{% block content %}
<div class="row">
    <!-- Prediction Result Card -->
    <div class="col-lg-8 mx-auto mb-4">
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h2 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i> Prediction Result
                </h2>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h3 class="text-success">Estimated Price</h3>
                        <h1 class="display-4 text-primary">${{ prediction.predicted_price|floatformat:0 }}</h1>
                        <p class="text-muted">Prediction ID: <code>{{ prediction.prediction_id }}</code></p>
                        <p class="text-muted">Generated: {{ prediction.created_at|date:"M d, Y H:i" }}</p>
                    </div>
                    <div class="col-md-6">
                        <h5>Property Summary</h5>
                        <ul class="list-unstyled">
                            <li><strong>Bedrooms:</strong> {{ prediction.bedrooms }}</li>
                            <li><strong>Bathrooms:</strong> {{ prediction.bathrooms }}</li>
                            <li><strong>Living Space:</strong> {{ prediction.sqft_living|floatformat:0 }} sq ft</li>
                            <li><strong>Lot Size:</strong> {{ prediction.sqft_lot|floatformat:0 }} sq ft</li>
                            <li><strong>Year Built:</strong> {{ prediction.yr_built }}</li>
                            <li><strong>Condition:</strong> {{ prediction.get_condition_display|default:prediction.condition }}/5</li>
                        </ul>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="btn-group w-100" role="group">
                            <a href="{% url 'home' %}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> New Prediction
                            </a>
                            <button onclick="window.print()" class="btn btn-secondary">
                                <i class="fas fa-print"></i> Print Report
                            </button>
                            <button onclick="shareResult()" class="btn btn-info">
                                <i class="fas fa-share"></i> Share
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Property Information -->
<div class="row mb-4">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Detailed Property Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">Basic Features</h6>
                        <table class="table table-sm">
                            <tr><td>Sale Year</td><td>{{ prediction.year }}</td></tr>
                            <tr><td>Bedrooms</td><td>{{ prediction.bedrooms }}</td></tr>
                            <tr><td>Bathrooms</td><td>{{ prediction.bathrooms }}</td></tr>
                            <tr><td>Floors</td><td>{{ prediction.floors }}</td></tr>
                            <tr><td>Waterfront</td><td>{% if prediction.waterfront %}Yes{% else %}No{% endif %}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">Size & Quality</h6>
                        <table class="table table-sm">
                            <tr><td>Living Space</td><td>{{ prediction.sqft_living|floatformat:0 }} sq ft</td></tr>
                            <tr><td>Above Ground</td><td>{{ prediction.sqft_above|floatformat:0 }} sq ft</td></tr>
                            <tr><td>Basement</td><td>{{ prediction.sqft_basement|floatformat:0 }} sq ft</td></tr>
                            <tr><td>Lot Size</td><td>{{ prediction.sqft_lot|floatformat:0 }} sq ft</td></tr>
                            <tr><td>Grade</td><td>{{ prediction.grade }}/13</td></tr>
                        </table>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6 class="text-primary">Property History</h6>
                        <table class="table table-sm">
                            <tr><td>Year Built</td><td>{{ prediction.yr_built }}</td></tr>
                            <tr><td>Year Renovated</td><td>{% if prediction.yr_renovated %}{{ prediction.yr_renovated }}{% else %}Never{% endif %}</td></tr>
                            <tr><td>Renovated</td><td>{% if prediction.is_renovated %}Yes{% else %}No{% endif %}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">Neighborhood</h6>
                        <table class="table table-sm">
                            <tr><td>Latitude</td><td>{{ prediction.lat|floatformat:4 }}</td></tr>
                            <tr><td>Avg Neighbor Living Space</td><td>{{ prediction.sqft_living15|floatformat:0 }} sq ft</td></tr>
                            <tr><td>Avg Neighbor Lot Size</td><td>{{ prediction.sqft_lot15|floatformat:0 }} sq ft</td></tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Price Trends Chart -->
<div class="row mb-4">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-area"></i> Market Price Trends</h5>
            </div>
            <div class="card-body">
                <canvas id="priceChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Historical Predictions -->
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history"></i> Recent Predictions</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">View your recent predictions and compare market trends.</p>
                <a href="{% url 'prediction_history' %}" class="btn btn-outline-primary">
                    <i class="fas fa-list"></i> View All Predictions
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
@media print {
    .btn, .card-header, nav, footer {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    .display-4 {
        font-size: 2rem !important;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Price trends chart
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('priceChart').getContext('2d');

    // Sample data for demonstration
    const trendData = [
        {year: 2022, month: 1, average_price: 450000},
        {year: 2022, month: 6, average_price: 465000},
        {year: 2022, month: 12, average_price: 480000},
        {year: 2023, month: 6, average_price: 495000},
        {year: 2023, month: 12, average_price: 510000},
        {year: 2024, month: 6, average_price: 525000}
    ];

    const labels = trendData.map(item => `${item.year}-${item.month.toString().padStart(2, '0')}`);
    const prices = trendData.map(item => item.average_price);

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Average Price',
                data: prices,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'House Price Trends'
                }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
});

// Share functionality
function shareResult() {
    const url = window.location.href;
    const text = `Check out this house price prediction: ${{ prediction.predicted_price|floatformat:0 }}`;
    
    if (navigator.share) {
        navigator.share({
            title: 'House Price Prediction',
            text: text,
            url: url
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(`${text} - ${url}`).then(() => {
            alert('Link copied to clipboard!');
        });
    }
}
</script>
{% endblock %}
