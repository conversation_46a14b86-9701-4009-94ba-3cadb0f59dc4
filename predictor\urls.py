"""
URL configuration for the predictor app
"""

from django.urls import path
from . import views

urlpatterns = [
    path('', views.HomeView.as_view(), name='home'),
    path('predict/', views.predict_price, name='predict_price'),
    path('result/<uuid:prediction_id>/', views.prediction_result, name='prediction_result'),
    path('search/', views.search_prediction, name='search_prediction'),
    path('history/', views.prediction_history, name='prediction_history'),
    
    # API endpoints
    path('api/predict/', views.api_predict, name='api_predict'),
    path('api/trends/', views.price_trends_api, name='price_trends_api'),
]
