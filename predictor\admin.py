from django.contrib import admin
from .models import PredictionRequest, PriceTrend

@admin.register(PredictionRequest)
class PredictionRequestAdmin(admin.ModelAdmin):
    list_display = ['prediction_id', 'bedrooms', 'bathrooms', 'sqft_living', 'predicted_price', 'created_at']
    list_filter = ['created_at', 'bedrooms', 'bathrooms', 'waterfront']
    search_fields = ['prediction_id']
    readonly_fields = ['prediction_id', 'predicted_price', 'log_predicted_price', 'created_at', 'updated_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('prediction_id', 'year', 'bedrooms', 'bathrooms')
        }),
        ('Property Size', {
            'fields': ('sqft_living', 'sqft_lot', 'sqft_above', 'sqft_basement', 'floors')
        }),
        ('Property Features', {
            'fields': ('waterfront', 'view', 'condition', 'grade')
        }),
        ('Property History', {
            'fields': ('yr_built', 'yr_renovated', 'is_renovated')
        }),
        ('Location', {
            'fields': ('lat', 'sqft_living15', 'sqft_lot15')
        }),
        ('Prediction Results', {
            'fields': ('predicted_price', 'log_predicted_price')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at')
        }),
    )

@admin.register(PriceTrend)
class PriceTrendAdmin(admin.ModelAdmin):
    list_display = ['year', 'month', 'average_price', 'median_price', 'count', 'created_at']
    list_filter = ['year', 'month']
    ordering = ['-year', '-month']
