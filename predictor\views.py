from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views.generic import TemplateView
import json
import logging

from .models import PredictionRequest, PriceTrend
from .forms import HousePredictionForm, PredictionSearchForm
from .ml_service import predictor

logger = logging.getLogger(__name__)

class HomeView(TemplateView):
    """Home page with prediction form"""
    template_name = 'predictor/home.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = HousePredictionForm()
        context['search_form'] = PredictionSearchForm()
        return context

def predict_price(request):
    """Handle price prediction requests"""
    if request.method == 'POST':
        form = HousePredictionForm(request.POST)

        if form.is_valid():
            try:
                # Save the prediction request
                prediction_request = form.save()

                # Make prediction using ML service
                predicted_price, log_price = predictor.predict_price(prediction_request)

                # Update the prediction request with results
                prediction_request.predicted_price = predicted_price
                prediction_request.log_predicted_price = log_price
                prediction_request.save()

                messages.success(
                    request,
                    f'Prediction completed! Estimated price: ${predicted_price:,.2f}'
                )

                return redirect('prediction_result', prediction_id=prediction_request.prediction_id)

            except Exception as e:
                logger.error(f"Prediction error: {e}")
                messages.error(request, f'Error making prediction: {str(e)}')
        else:
            messages.error(request, 'Please correct the errors in the form.')
    else:
        form = HousePredictionForm()

    return render(request, 'predictor/home.html', {
        'form': form,
        'search_form': PredictionSearchForm()
    })

def prediction_result(request, prediction_id):
    """Display prediction results"""
    prediction = get_object_or_404(PredictionRequest, prediction_id=prediction_id)

    # Get price trends for visualization
    trends = predictor.get_price_trends()

    context = {
        'prediction': prediction,
        'trends': trends,
        'search_form': PredictionSearchForm()
    }

    return render(request, 'predictor/result.html', context)

def search_prediction(request):
    """Search for prediction by ID"""
    if request.method == 'POST':
        form = PredictionSearchForm(request.POST)

        if form.is_valid():
            prediction_id = form.cleaned_data['prediction_id']
            try:
                prediction = PredictionRequest.objects.get(prediction_id=prediction_id)
                return redirect('prediction_result', prediction_id=prediction_id)
            except PredictionRequest.DoesNotExist:
                messages.error(request, 'Prediction not found with the given ID.')
        else:
            messages.error(request, 'Please enter a valid prediction ID.')

    return redirect('home')

def prediction_history(request):
    """Display recent predictions"""
    predictions = PredictionRequest.objects.filter(
        predicted_price__isnull=False
    ).order_by('-created_at')[:20]

    context = {
        'predictions': predictions,
        'search_form': PredictionSearchForm()
    }

    return render(request, 'predictor/history.html', context)

@csrf_exempt
def api_predict(request):
    """API endpoint for price prediction"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)

            # Create prediction request from API data
            prediction_request = PredictionRequest(**data)
            prediction_request.save()

            # Make prediction
            predicted_price, log_price = predictor.predict_price(prediction_request)

            # Update with results
            prediction_request.predicted_price = predicted_price
            prediction_request.log_predicted_price = log_price
            prediction_request.save()

            return JsonResponse({
                'success': True,
                'prediction_id': str(prediction_request.prediction_id),
                'predicted_price': predicted_price,
                'formatted_price': f'${predicted_price:,.2f}'
            })

        except Exception as e:
            logger.error(f"API prediction error: {e}")
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=400)

    return JsonResponse({'error': 'Method not allowed'}, status=405)

def price_trends_api(request):
    """API endpoint for price trends data"""
    try:
        trends = predictor.get_price_trends()
        return JsonResponse({
            'success': True,
            'trends': trends
        })
    except Exception as e:
        logger.error(f"Price trends API error: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)
