"""
Machine Learning Service for House Price Prediction
Integrates the trained PySpark model for making predictions
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import <PERSON>radientBoostingRegressor
from sklearn.model_selection import train_test_split
import pickle
import os
from django.conf import settings
from .models import PredictionRequest, PriceTrend
import logging

logger = logging.getLogger(__name__)

class HousePricePredictor:
    """Service class for house price prediction using the trained model"""
    
    def __init__(self):
        self.model = None
        self.feature_columns = [
            'Year', 'bedrooms', 'bathrooms', 'sqft_living', 'sqft_lot', 
            'floors', 'waterfront', 'view', 'condition', 'grade', 
            'sqft_above', 'sqft_basement', 'yr_built', 'yr_renovated', 
            'lat', 'sqft_living15', 'sqft_lot15', 'is_renovated', 'total_room'
        ]
        self.model_path = os.path.join(settings.BASE_DIR, 'predictor', 'trained_model.pkl')
        self.load_or_train_model()
    
    def load_or_train_model(self):
        """Load existing model or create a simple demo model"""
        try:
            if os.path.exists(self.model_path):
                with open(self.model_path, 'rb') as f:
                    self.model = pickle.load(f)
                logger.info("Model loaded successfully")
            else:
                logger.info("No existing model found, creating demo model...")
                self.create_demo_model()
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            self.create_demo_model()
    
    def create_demo_model(self):
        """Create a simple demo model for fast predictions"""
        try:
            # Create synthetic training data for demo purposes
            np.random.seed(42)
            n_samples = 1000

            # Generate synthetic features
            X_demo = np.random.rand(n_samples, len(self.feature_columns))

            # Scale features to realistic ranges
            feature_ranges = {
                0: (2020, 2024),      # Year
                1: (1, 6),            # bedrooms
                2: (1, 4),            # bathrooms
                3: (800, 4000),       # sqft_living
                4: (3000, 15000),     # sqft_lot
                5: (1, 3),            # floors
                6: (0, 1),            # waterfront
                7: (0, 4),            # view
                8: (1, 5),            # condition
                9: (3, 12),           # grade
                10: (600, 3000),      # sqft_above
                11: (0, 1000),        # sqft_basement
                12: (1950, 2020),     # yr_built
                13: (0, 2020),        # yr_renovated
                14: (47.1, 47.8),     # lat
                15: (800, 3500),      # sqft_living15
                16: (3000, 12000),    # sqft_lot15
                17: (0, 1),           # is_renovated
                18: (2, 10),          # total_room
            }

            for i, (min_val, max_val) in feature_ranges.items():
                X_demo[:, i] = X_demo[:, i] * (max_val - min_val) + min_val

            # Create realistic price targets based on features
            y_demo = (
                X_demo[:, 1] * 50000 +      # bedrooms
                X_demo[:, 2] * 30000 +      # bathrooms
                X_demo[:, 3] * 150 +        # sqft_living
                X_demo[:, 9] * 20000 +      # grade
                X_demo[:, 6] * 100000 +     # waterfront
                np.random.normal(400000, 50000, n_samples)  # base price + noise
            )

            # Ensure positive prices
            y_demo = np.maximum(y_demo, 200000)

            # Log transform for training
            y_demo_log = np.log(y_demo)

            # Train a simple model
            self.model = GradientBoostingRegressor(
                n_estimators=50,  # Fewer estimators for speed
                max_depth=5,      # Reduced depth
                random_state=42
            )
            self.model.fit(X_demo, y_demo_log)

            # Save the model
            os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
            with open(self.model_path, 'wb') as f:
                pickle.dump(self.model, f)

            logger.info("Demo model created and saved successfully")

        except Exception as e:
            logger.error(f"Error creating demo model: {e}")
            raise
    
    def preprocess_data(self, df):
        """Preprocess data following the original notebook steps"""
        # Remove commas from price and convert to float
        if 'price' in df.columns:
            df['price'] = df['price'].astype(str).str.replace(',', '').astype(float)
        
        # Parse date and extract year/month
        if 'date' in df.columns:
            df['DateTime'] = pd.to_datetime(df['date'])
            df['Year'] = df['DateTime'].dt.year
            df['Month'] = df['DateTime'].dt.month
        
        # Create derived features
        df['is_renovated'] = (df['yr_renovated'] > 0).astype(int)
        df['total_room'] = df['bedrooms'] + df['bathrooms'].round(0).astype(int)
        
        # Handle missing values and outliers
        df = df.dropna()
        
        # Filter outliers (bedrooms <= 10 as in original)
        df = df[df['bedrooms'] <= 10]
        
        return df
    
    def predict_price(self, prediction_request):
        """Make price prediction for a given request"""
        try:
            if self.model is None:
                raise ValueError("Model not loaded")
            
            # Prepare input features
            features = self.prepare_features(prediction_request)
            
            # Make prediction (returns log price)
            log_price = self.model.predict([features])[0]
            
            # Convert back to actual price
            predicted_price = np.exp(log_price)
            
            return predicted_price, log_price
            
        except Exception as e:
            logger.error(f"Error making prediction: {e}")
            raise
    
    def prepare_features(self, prediction_request):
        """Prepare features from prediction request"""
        features = []
        
        for col in self.feature_columns:
            if col == 'Year':
                features.append(prediction_request.year)
            elif col == 'is_renovated':
                features.append(1 if prediction_request.yr_renovated > 0 else 0)
            elif col == 'total_room':
                features.append(prediction_request.bedrooms + int(prediction_request.bathrooms))
            elif col == 'waterfront':
                features.append(1 if prediction_request.waterfront else 0)
            else:
                features.append(getattr(prediction_request, col))
        
        return features
    
    def get_price_trends(self, years=5):
        """Get historical price trends for visualization"""
        try:
            # This would typically come from historical data
            # For now, we'll generate sample trend data
            trends = []
            base_price = 500000
            
            for year in range(2019, 2024):
                for month in range(1, 13):
                    # Simulate seasonal trends
                    seasonal_factor = 1 + 0.1 * np.sin(2 * np.pi * month / 12)
                    yearly_growth = 1.05 ** (year - 2019)  # 5% annual growth
                    
                    avg_price = base_price * yearly_growth * seasonal_factor
                    
                    trends.append({
                        'year': year,
                        'month': month,
                        'average_price': avg_price,
                        'median_price': avg_price * 0.9,  # Median typically lower
                        'count': np.random.randint(50, 200)
                    })
            
            return trends
            
        except Exception as e:
            logger.error(f"Error getting price trends: {e}")
            return []

# Global instance
predictor = HousePricePredictor()
