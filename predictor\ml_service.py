"""
Machine Learning Service for House Price Prediction
Integrates the trained PySpark model for making predictions
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import GradientBoostingRegressor
from sklearn.model_selection import train_test_split
import pickle
import os
from django.conf import settings
from .models import PredictionRequest, PriceTrend
import logging

logger = logging.getLogger(__name__)

class HousePricePredictor:
    """Service class for house price prediction using the trained model"""
    
    def __init__(self):
        self.model = None
        self.feature_columns = [
            'Year', 'bedrooms', 'bathrooms', 'sqft_living', 'sqft_lot', 
            'floors', 'waterfront', 'view', 'condition', 'grade', 
            'sqft_above', 'sqft_basement', 'yr_built', 'yr_renovated', 
            'lat', 'sqft_living15', 'sqft_lot15', 'is_renovated', 'total_room'
        ]
        self.model_path = os.path.join(settings.BASE_DIR, 'predictor', 'trained_model.pkl')
        self.load_or_train_model()
    
    def load_or_train_model(self):
        """Load existing model or train a new one"""
        try:
            if os.path.exists(self.model_path):
                with open(self.model_path, 'rb') as f:
                    self.model = pickle.load(f)
                logger.info("Model loaded successfully")
            else:
                logger.info("No existing model found, training new model...")
                self.train_model()
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            self.train_model()
    
    def train_model(self):
        """Train the model using the original dataset"""
        try:
            # Load the original dataset
            data_path = os.path.join(settings.BASE_DIR, 'Housedata.csv')
            if not os.path.exists(data_path):
                raise FileNotFoundError(f"Dataset not found at {data_path}")
            
            # Load and preprocess data (following the original notebook logic)
            df = pd.read_csv(data_path)
            
            # Data preprocessing steps from the original notebook
            df = self.preprocess_data(df)
            
            # Prepare features and target
            X = df[self.feature_columns]
            y = np.log(df['price'])  # Log transformation as in original model
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=0
            )
            
            # Train model (using same parameters as original)
            self.model = GradientBoostingRegressor(max_depth=7, random_state=42)
            self.model.fit(X_train, y_train)
            
            # Save the trained model
            os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
            with open(self.model_path, 'wb') as f:
                pickle.dump(self.model, f)
            
            logger.info("Model trained and saved successfully")
            
            # Calculate and log performance
            train_score = self.model.score(X_train, y_train)
            test_score = self.model.score(X_test, y_test)
            logger.info(f"Model performance - Train R²: {train_score:.4f}, Test R²: {test_score:.4f}")
            
        except Exception as e:
            logger.error(f"Error training model: {e}")
            raise
    
    def preprocess_data(self, df):
        """Preprocess data following the original notebook steps"""
        # Remove commas from price and convert to float
        if 'price' in df.columns:
            df['price'] = df['price'].astype(str).str.replace(',', '').astype(float)
        
        # Parse date and extract year/month
        if 'date' in df.columns:
            df['DateTime'] = pd.to_datetime(df['date'])
            df['Year'] = df['DateTime'].dt.year
            df['Month'] = df['DateTime'].dt.month
        
        # Create derived features
        df['is_renovated'] = (df['yr_renovated'] > 0).astype(int)
        df['total_room'] = df['bedrooms'] + df['bathrooms'].round(0).astype(int)
        
        # Handle missing values and outliers
        df = df.dropna()
        
        # Filter outliers (bedrooms <= 10 as in original)
        df = df[df['bedrooms'] <= 10]
        
        return df
    
    def predict_price(self, prediction_request):
        """Make price prediction for a given request"""
        try:
            if self.model is None:
                raise ValueError("Model not loaded")
            
            # Prepare input features
            features = self.prepare_features(prediction_request)
            
            # Make prediction (returns log price)
            log_price = self.model.predict([features])[0]
            
            # Convert back to actual price
            predicted_price = np.exp(log_price)
            
            return predicted_price, log_price
            
        except Exception as e:
            logger.error(f"Error making prediction: {e}")
            raise
    
    def prepare_features(self, prediction_request):
        """Prepare features from prediction request"""
        features = []
        
        for col in self.feature_columns:
            if col == 'Year':
                features.append(prediction_request.year)
            elif col == 'is_renovated':
                features.append(1 if prediction_request.yr_renovated > 0 else 0)
            elif col == 'total_room':
                features.append(prediction_request.bedrooms + int(prediction_request.bathrooms))
            elif col == 'waterfront':
                features.append(1 if prediction_request.waterfront else 0)
            else:
                features.append(getattr(prediction_request, col))
        
        return features
    
    def get_price_trends(self, years=5):
        """Get historical price trends for visualization"""
        try:
            # This would typically come from historical data
            # For now, we'll generate sample trend data
            trends = []
            base_price = 500000
            
            for year in range(2019, 2024):
                for month in range(1, 13):
                    # Simulate seasonal trends
                    seasonal_factor = 1 + 0.1 * np.sin(2 * np.pi * month / 12)
                    yearly_growth = 1.05 ** (year - 2019)  # 5% annual growth
                    
                    avg_price = base_price * yearly_growth * seasonal_factor
                    
                    trends.append({
                        'year': year,
                        'month': month,
                        'average_price': avg_price,
                        'median_price': avg_price * 0.9,  # Median typically lower
                        'count': np.random.randint(50, 200)
                    })
            
            return trends
            
        except Exception as e:
            logger.error(f"Error getting price trends: {e}")
            return []

# Global instance
predictor = HousePricePredictor()
