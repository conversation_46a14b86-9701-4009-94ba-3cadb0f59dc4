/* Custom styles for House Price Prediction App */

:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* Forms */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
}

/* Price Display */
.display-4 {
    font-weight: 700;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

/* Tables */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Charts */
canvas {
    border-radius: 8px;
}

/* Alerts */
.alert {
    border-radius: 8px;
    border: none;
}

/* Print Styles */
@media print {
    body {
        background-color: white !important;
    }
    
    .navbar, .btn, .card-header, footer {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
        margin-bottom: 1rem !important;
    }
    
    .container {
        max-width: 100% !important;
        padding: 0 !important;
    }
    
    .display-4 {
        font-size: 2.5rem !important;
    }
    
    .table {
        font-size: 0.9rem;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .display-4 {
        font-size: 2rem;
    }
    
    .btn-group.w-100 .btn {
        font-size: 0.9rem;
        padding: 0.5rem;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Custom Icons */
.fas {
    margin-right: 0.5rem;
}

/* Tooltips */
.tooltip {
    font-size: 0.875rem;
}

/* Form Sections */
.border-bottom {
    border-bottom: 2px solid var(--primary-color) !important;
}

/* Success States */
.text-success {
    color: var(--success-color) !important;
}

.bg-success {
    background-color: var(--success-color) !important;
}

/* Custom Spacing */
.py-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
}

/* Shadow Effects */
.shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

/* Hover Effects */
.card-hover:hover {
    box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.2) !important;
    transform: translateY(-3px);
}

/* Code Styling */
code {
    background-color: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.875rem;
}
